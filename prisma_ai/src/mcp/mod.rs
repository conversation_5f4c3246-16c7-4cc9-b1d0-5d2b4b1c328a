// =================================================================================================
// File: /prisma_ai/src/mcp/mod.rs
// =================================================================================================
// Purpose: Main module for Model Context Protocol (MCP) integration.
// This module provides comprehensive MCP functionality including client/server implementations,
// transport layer, tools registry, and agent manager integration.
// =================================================================================================
// Internal Dependencies:
// - All submodules for MCP functionality
// =================================================================================================
// External Dependencies:
// - Standard library and external crates as needed by submodules
// =================================================================================================
// Module Interactions:
// - Provides MCP functionality to the broader Prisma AI system
// - Integrates with agent manager for tool exposure and consumption
// - Used by UI and configuration systems for MCP management
// =================================================================================================

pub mod client;
pub mod server;
pub mod transport;
pub mod tools;
pub mod integration;

// Re-export commonly used types for convenience
pub use client::{McpClient, McpClientManager, McpClientConfig, McpTool, McpToolResult, McpContent};
pub use server::{McpServer, McpServerConfig, ToolHandler, ResourceHandler};
pub use transport::{TransportConfig, TransportFactory, McpTransport};
pub use tools::registry::{McpToolsRegistry, RegisteredTool, ToolSource, ToolExecutionResult, ToolExecutionContext};
pub use tools::handlers::{FileSystemHandler, HttpHandler, LlmHandler, EmbeddingHandler};
pub use integration::{
    McpIntegrationManager, McpIntegrationConfig, McpMarketplaceConfig,
    AgentCapabilityMapping, MarketplaceTool, McpIntegrationStatus,
    create_default_config,
};
