// =================================================================================================
// File: /prisma_ai/src/mcp/tools/handlers.rs
// =================================================================================================
// Purpose: Implements specific tool handlers for common MCP operations and agent capabilities.
// This file provides concrete implementations of tool handlers that can be registered with
// the MCP tools registry and exposed through MCP servers.
// =================================================================================================
// Internal Dependencies:
// - crate::err: For error handling and result types
// - super::super::client: For MCP client types and tool results
// - super::super::server: For tool handler trait
// - crate::prisma::prisma_engine: For accessing agent capabilities
// - crate::storage: For database operations
// =================================================================================================
// External Dependencies:
// - tokio: For async runtime and file operations
// - serde: For JSON serialization/deserialization
// - tracing: For logging and debugging
// - std::path: For file system operations
// - reqwest: For HTTP operations
// =================================================================================================
// Module Interactions:
// - Implements ToolHandler trait from server module
// - Used by tools registry for tool execution
// - Integrates with agent manager for accessing agent capabilities
// - Provides common tool implementations for MCP servers
// =================================================================================================

use std::path::Path;
use std::sync::Arc;
use std::time::Duration;
use tokio::fs;
use tokio::io::AsyncReadExt;
use tracing::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use anyhow::{Context, Result};

use crate::err::{PrismaResult, GenericError};
use super::super::client::{McpToolResult, McpContent};
use super::super::server::ToolHandler;
use crate::llm::implementation::service::LlmServiceImpl;
use crate::llm::implementation::embedding::EmbeddingServiceImpl;
use crate::llm::interface::embedding::EmbeddingGenerator;
use crate::prisma::prisma_engine::tcl::llm_task::LlmService;
use crate::{ModelParams, ContextParams};

/// File system operations tool handler
pub struct FileSystemHandler {
    /// Base directory for file operations (for security)
    base_dir: std::path::PathBuf,
    /// Whether to allow write operations
    allow_write: bool,
}

impl FileSystemHandler {
    /// Create a new file system handler
    pub fn new(base_dir: impl AsRef<Path>, allow_write: bool) -> Self {
        let base_dir = base_dir.as_ref().to_path_buf();
        info!("Creating file system handler with base dir: {:?}, write allowed: {}", base_dir, allow_write);

        Self {
            base_dir,
            allow_write,
        }
    }

    /// Validate that a path is within the base directory
    fn validate_path(&self, path: &str) -> PrismaResult<std::path::PathBuf> {
        let requested_path = Path::new(path);

        // Resolve the path relative to base directory
        let full_path = if requested_path.is_absolute() {
            requested_path.to_path_buf()
        } else {
            self.base_dir.join(requested_path)
        };

        // Canonicalize to resolve any .. or . components
        let canonical_path = full_path.canonicalize()
            .map_err(|e| GenericError::new(e))?;

        // Check if the canonical path is within the base directory
        if !canonical_path.starts_with(&self.base_dir) {
            return Err(GenericError::from(format!("Path '{}' is outside allowed directory", path)).into());
        }

        Ok(canonical_path)
    }
}

#[async_trait::async_trait]
impl ToolHandler for FileSystemHandler {
    async fn execute(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        debug!("Executing file system operation with arguments: {}", arguments);

        let operation = arguments.get("operation")
            .and_then(|op| op.as_str())
            .ok_or_else(|| GenericError::from("Missing 'operation' parameter"))?;

        match operation {
            "read" => self.handle_read(arguments).await,
            "write" => self.handle_write(arguments).await,
            "list" => self.handle_list(arguments).await,
            "exists" => self.handle_exists(arguments).await,
            "delete" => self.handle_delete(arguments).await,
            _ => Err(GenericError::from(format!("Unknown file system operation: {}", operation)).into()),
        }
    }
}

impl FileSystemHandler {
    /// Handle file read operation
    async fn handle_read(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let path_str = arguments.get("path")
            .and_then(|p| p.as_str())
            .ok_or_else(|| GenericError::from("Missing 'path' parameter"))?;

        let path = self.validate_path(path_str)?;

        debug!("Reading file: {:?}", path);

        match fs::read_to_string(&path).await {
            Ok(content) => {
                info!("Successfully read file: {:?}", path);
                Ok(McpToolResult {
                    content: vec![McpContent::Text { text: content }],
                    is_error: Some(false),
                })
            }
            Err(e) => {
                error!("Failed to read file {:?}: {}", path, e);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Error reading file: {}", e)
                    }],
                    is_error: Some(true),
                })
            }
        }
    }

    /// Handle file write operation
    async fn handle_write(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        if !self.allow_write {
            return Ok(McpToolResult {
                content: vec![McpContent::Text {
                    text: "Write operations are not allowed".to_string()
                }],
                is_error: Some(true),
            });
        }

        let path_str = arguments.get("path")
            .and_then(|p| p.as_str())
            .ok_or_else(|| GenericError::from("Missing 'path' parameter"))?;

        let content = arguments.get("content")
            .and_then(|c| c.as_str())
            .ok_or_else(|| GenericError::from("Missing 'content' parameter"))?;

        let path = self.validate_path(path_str)?;

        debug!("Writing to file: {:?}", path);

        // Create parent directories if they don't exist
        if let Some(parent) = path.parent() {
            if let Err(e) = fs::create_dir_all(parent).await {
                error!("Failed to create parent directories for {:?}: {}", path, e);
                return Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Error creating directories: {}", e)
                    }],
                    is_error: Some(true),
                });
            }
        }

        match fs::write(&path, content).await {
            Ok(_) => {
                info!("Successfully wrote to file: {:?}", path);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Successfully wrote {} bytes to {}", content.len(), path_str)
                    }],
                    is_error: Some(false),
                })
            }
            Err(e) => {
                error!("Failed to write to file {:?}: {}", path, e);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Error writing file: {}", e)
                    }],
                    is_error: Some(true),
                })
            }
        }
    }

    /// Handle directory listing operation
    async fn handle_list(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let path_str = arguments.get("path")
            .and_then(|p| p.as_str())
            .unwrap_or(".");

        let path = self.validate_path(path_str)?;

        debug!("Listing directory: {:?}", path);

        match fs::read_dir(&path).await {
            Ok(mut entries) => {
                let mut files = Vec::new();

                while let Some(entry) = entries.next_entry().await.transpose() {
                    match entry {
                        Ok(entry) => {
                            let file_name = entry.file_name().to_string_lossy().to_string();
                            let metadata = entry.metadata().await;

                            let file_type = match metadata {
                                Ok(meta) if meta.is_dir() => "directory",
                                Ok(meta) if meta.is_file() => "file",
                                _ => "unknown",
                            };

                            files.push(format!("{} ({})", file_name, file_type));
                        }
                        Err(e) => {
                            warn!("Error reading directory entry: {}", e);
                        }
                    }
                }

                let content = files.join("\n");
                info!("Successfully listed directory: {:?} ({} entries)", path, files.len());

                Ok(McpToolResult {
                    content: vec![McpContent::Text { text: content }],
                    is_error: Some(false),
                })
            }
            Err(e) => {
                error!("Failed to list directory {:?}: {}", path, e);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Error listing directory: {}", e)
                    }],
                    is_error: Some(true),
                })
            }
        }
    }

    /// Handle file existence check
    async fn handle_exists(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let path_str = arguments.get("path")
            .and_then(|p| p.as_str())
            .ok_or_else(|| GenericError::from("Missing 'path' parameter"))?;

        let path = self.validate_path(path_str)?;

        debug!("Checking if path exists: {:?}", path);

        let exists = path.exists();
        let message = if exists {
            format!("Path '{}' exists", path_str)
        } else {
            format!("Path '{}' does not exist", path_str)
        };

        Ok(McpToolResult {
            content: vec![McpContent::Text { text: message }],
            is_error: Some(false),
        })
    }

    /// Handle file deletion
    async fn handle_delete(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        if !self.allow_write {
            return Ok(McpToolResult {
                content: vec![McpContent::Text {
                    text: "Delete operations are not allowed".to_string()
                }],
                is_error: Some(true),
            });
        }

        let path_str = arguments.get("path")
            .and_then(|p| p.as_str())
            .ok_or_else(|| GenericError::from("Missing 'path' parameter"))?;

        let path = self.validate_path(path_str)?;

        debug!("Deleting path: {:?}", path);

        let result = if path.is_dir() {
            fs::remove_dir_all(&path).await
        } else {
            fs::remove_file(&path).await
        };

        match result {
            Ok(_) => {
                info!("Successfully deleted: {:?}", path);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Successfully deleted '{}'", path_str)
                    }],
                    is_error: Some(false),
                })
            }
            Err(e) => {
                error!("Failed to delete {:?}: {}", path, e);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Error deleting path: {}", e)
                    }],
                    is_error: Some(true),
                })
            }
        }
    }
}

/// Playwright browser automation tool handler
pub struct PlaywrightHandler {
    /// Browser executable path (optional, uses system default if None)
    browser_path: Option<std::path::PathBuf>,
    /// Whether to run browser in headless mode
    headless: bool,
    /// Browser type (chromium, firefox, webkit)
    browser_type: String,
    /// Allowed domains for navigation (for security)
    allowed_domains: Option<Vec<String>>,
    /// Browser launch arguments
    launch_args: Vec<String>,
}

impl PlaywrightHandler {
    /// Create a new Playwright handler
    pub fn new(
        browser_type: Option<String>,
        headless: bool,
        browser_path: Option<std::path::PathBuf>,
        allowed_domains: Option<Vec<String>>,
        launch_args: Option<Vec<String>>,
    ) -> Self {
        let browser_type = browser_type.unwrap_or_else(|| "chromium".to_string());
        let launch_args = launch_args.unwrap_or_else(|| {
            let mut args = vec![
                "--no-sandbox".to_string(),
                "--disable-dev-shm-usage".to_string(),
                "--disable-gpu".to_string(),
            ];
            if headless {
                args.push("--headless".to_string());
            }
            args
        });

        info!("Creating Playwright handler: browser={}, headless={}, allowed_domains={:?}",
              browser_type, headless, allowed_domains);

        Self {
            browser_path,
            headless,
            browser_type,
            allowed_domains,
            launch_args,
        }
    }

    /// Validate that a URL is allowed for navigation
    fn validate_url(&self, url: &str) -> PrismaResult<String> {
        let parsed_url = reqwest::Url::parse(url)
            .map_err(|e| GenericError::new(e))?;

        if let Some(ref allowed_domains) = self.allowed_domains {
            if let Some(domain) = parsed_url.domain() {
                if !allowed_domains.iter().any(|allowed| domain.ends_with(allowed)) {
                    return Err(GenericError::from(format!("Domain '{}' is not allowed", domain)).into());
                }
            } else {
                return Err(GenericError::from("URL must have a domain").into());
            }
        }

        Ok(url.to_string())
    }

    /// Launch browser and execute a command
    async fn execute_browser_command(&self, command: &str, args: &[String]) -> PrismaResult<String> {
        debug!("Executing browser command: {} with args: {:?}", command, args);

        // Determine browser executable
        let browser_executable = if let Some(ref path) = self.browser_path {
            path.to_string_lossy().to_string()
        } else {
            // Use system browser based on type
            match self.browser_type.as_str() {
                "firefox" => "firefox".to_string(),
                "webkit" | "safari" => "safari".to_string(),
                _ => "google-chrome".to_string(), // Default to Chrome for chromium
            }
        };

        // Build command arguments
        let mut cmd_args = self.launch_args.clone();
        cmd_args.extend_from_slice(args);

        // Execute browser command
        let output = tokio::process::Command::new(&browser_executable)
            .args(&cmd_args)
            .output()
            .await
            .map_err(|e| GenericError::from(format!("Failed to execute browser command: {}", e)))?;

        if output.status.success() {
            let stdout = String::from_utf8_lossy(&output.stdout);
            Ok(stdout.to_string())
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            Err(GenericError::from(format!("Browser command failed: {}", stderr)).into())
        }
    }
}

/// HTTP operations tool handler
pub struct HttpHandler {
    /// HTTP client
    client: reqwest::Client,
    /// Allowed domains (for security)
    allowed_domains: Option<Vec<String>>,
}

impl HttpHandler {
    /// Create a new HTTP handler
    pub fn new(allowed_domains: Option<Vec<String>>) -> Self {
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        info!("Creating HTTP handler with allowed domains: {:?}", allowed_domains);

        Self {
            client,
            allowed_domains,
        }
    }

    /// Validate that a URL is allowed
    fn validate_url(&self, url: &str) -> PrismaResult<reqwest::Url> {
        let parsed_url = reqwest::Url::parse(url)
            .map_err(|e| GenericError::new(e))?;

        if let Some(ref allowed_domains) = self.allowed_domains {
            if let Some(domain) = parsed_url.domain() {
                if !allowed_domains.iter().any(|allowed| domain.ends_with(allowed)) {
                    return Err(GenericError::from(format!("Domain '{}' is not allowed", domain)).into());
                }
            } else {
                return Err(GenericError::from("URL must have a domain").into());
            }
        }

        Ok(parsed_url)
    }
}

#[async_trait::async_trait]
impl ToolHandler for PlaywrightHandler {
    async fn execute(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        debug!("Executing Playwright operation with arguments: {}", arguments);

        let operation = arguments.get("operation")
            .and_then(|op| op.as_str())
            .ok_or_else(|| GenericError::from("Missing 'operation' parameter"))?;

        match operation {
            "navigate" => self.handle_navigate(arguments).await,
            "screenshot" => self.handle_screenshot(arguments).await,
            "get_title" => self.handle_get_title(arguments).await,
            "get_content" => self.handle_get_content(arguments).await,
            "click" => self.handle_click(arguments).await,
            "type" => self.handle_type(arguments).await,
            "wait" => self.handle_wait(arguments).await,
            _ => Err(GenericError::from(format!("Unknown Playwright operation: {}", operation)).into()),
        }
    }
}

impl PlaywrightHandler {
    /// Handle navigation to a URL
    async fn handle_navigate(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let url = arguments.get("url")
            .and_then(|u| u.as_str())
            .ok_or_else(|| GenericError::from("Missing 'url' parameter"))?;

        // Validate URL and handle validation errors gracefully
        let validated_url = match self.validate_url(url) {
            Ok(url) => url,
            Err(e) => {
                error!("URL validation failed for {}: {}", url, e);
                return Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("URL validation failed: {}", e),
                    }],
                    is_error: Some(true),
                });
            }
        };

        debug!("Navigating to: {}", validated_url);

        // For now, we'll simulate browser navigation using a simple HTTP request
        // In a full implementation, this would use actual browser automation
        let client = reqwest::Client::new();
        match client.get(&validated_url).send().await {
            Ok(response) => {
                let status = response.status();
                info!("Successfully navigated to: {} (status: {})", validated_url, status);

                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Navigated to {} (status: {})", validated_url, status),
                    }],
                    is_error: Some(false),
                })
            }
            Err(e) => {
                error!("Failed to navigate to {}: {}", validated_url, e);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Navigation failed: {}", e),
                    }],
                    is_error: Some(true),
                })
            }
        }
    }

    /// Handle taking a screenshot
    async fn handle_screenshot(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let url = arguments.get("url")
            .and_then(|u| u.as_str())
            .unwrap_or("about:blank");

        let output_path = arguments.get("output_path")
            .and_then(|p| p.as_str())
            .unwrap_or("/tmp/screenshot.png");

        debug!("Taking screenshot of: {} -> {}", url, output_path);

        // Build screenshot command arguments
        let args = vec![
            format!("--screenshot={}", output_path),
            url.to_string(),
        ];

        match self.execute_browser_command("screenshot", &args).await {
            Ok(output) => {
                info!("Screenshot saved to: {}", output_path);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Screenshot saved to: {}\nOutput: {}", output_path, output),
                    }],
                    is_error: Some(false),
                })
            }
            Err(e) => {
                error!("Failed to take screenshot: {}", e);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Screenshot failed: {}", e),
                    }],
                    is_error: Some(true),
                })
            }
        }
    }

    /// Handle getting page title
    async fn handle_get_title(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let url = arguments.get("url")
            .and_then(|u| u.as_str())
            .ok_or_else(|| GenericError::from("Missing 'url' parameter"))?;

        // Validate URL and handle validation errors gracefully
        let validated_url = match self.validate_url(url) {
            Ok(url) => url,
            Err(e) => {
                error!("URL validation failed for {}: {}", url, e);
                return Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("URL validation failed: {}", e),
                    }],
                    is_error: Some(true),
                });
            }
        };

        debug!("Getting title for: {}", validated_url);

        // For now, we'll get the title by fetching the page and parsing HTML
        let client = reqwest::Client::new();
        match client.get(&validated_url).send().await {
            Ok(response) => {
                match response.text().await {
                    Ok(html) => {
                        // Simple title extraction (in a full implementation, use proper HTML parsing)
                        let title = if let Some(start) = html.find("<title>") {
                            if let Some(end) = html[start + 7..].find("</title>") {
                                html[start + 7..start + 7 + end].to_string()
                            } else {
                                "No title found".to_string()
                            }
                        } else {
                            "No title found".to_string()
                        };

                        info!("Page title: {}", title);
                        Ok(McpToolResult {
                            content: vec![McpContent::Text { text: title }],
                            is_error: Some(false),
                        })
                    }
                    Err(e) => {
                        error!("Failed to read page content: {}", e);
                        Ok(McpToolResult {
                            content: vec![McpContent::Text {
                                text: format!("Failed to read page content: {}", e),
                            }],
                            is_error: Some(true),
                        })
                    }
                }
            }
            Err(e) => {
                error!("Failed to fetch page: {}", e);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Failed to fetch page: {}", e),
                    }],
                    is_error: Some(true),
                })
            }
        }
    }

    /// Handle getting page content
    async fn handle_get_content(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let url = arguments.get("url")
            .and_then(|u| u.as_str())
            .ok_or_else(|| GenericError::from("Missing 'url' parameter"))?;

        // Validate URL and handle validation errors gracefully
        let validated_url = match self.validate_url(url) {
            Ok(url) => url,
            Err(e) => {
                error!("URL validation failed for {}: {}", url, e);
                return Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("URL validation failed: {}", e),
                    }],
                    is_error: Some(true),
                });
            }
        };

        debug!("Getting content for: {}", validated_url);

        let client = reqwest::Client::new();
        match client.get(&validated_url).send().await {
            Ok(response) => {
                match response.text().await {
                    Ok(content) => {
                        info!("Retrieved page content ({} chars)", content.len());
                        Ok(McpToolResult {
                            content: vec![McpContent::Text { text: content }],
                            is_error: Some(false),
                        })
                    }
                    Err(e) => {
                        error!("Failed to read page content: {}", e);
                        Ok(McpToolResult {
                            content: vec![McpContent::Text {
                                text: format!("Failed to read page content: {}", e),
                            }],
                            is_error: Some(true),
                        })
                    }
                }
            }
            Err(e) => {
                error!("Failed to fetch page: {}", e);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Failed to fetch page: {}", e),
                    }],
                    is_error: Some(true),
                })
            }
        }
    }

    /// Handle clicking an element (simplified implementation)
    async fn handle_click(&self, _arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        // This is a simplified implementation
        // In a full implementation, this would use actual browser automation
        warn!("Click operation is not fully implemented - returning success for testing");

        Ok(McpToolResult {
            content: vec![McpContent::Text {
                text: "Click operation completed (simulated)".to_string(),
            }],
            is_error: Some(false),
        })
    }

    /// Handle typing text (simplified implementation)
    async fn handle_type(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let text = arguments.get("text")
            .and_then(|t| t.as_str())
            .unwrap_or("");

        debug!("Typing text: {}", text);

        // This is a simplified implementation
        // In a full implementation, this would use actual browser automation
        warn!("Type operation is not fully implemented - returning success for testing");

        Ok(McpToolResult {
            content: vec![McpContent::Text {
                text: format!("Typed text: '{}' (simulated)", text),
            }],
            is_error: Some(false),
        })
    }

    /// Handle waiting (simplified implementation)
    async fn handle_wait(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let duration = arguments.get("duration")
            .and_then(|d| d.as_u64())
            .unwrap_or(1000); // Default 1 second

        debug!("Waiting for {} ms", duration);

        tokio::time::sleep(Duration::from_millis(duration)).await;

        Ok(McpToolResult {
            content: vec![McpContent::Text {
                text: format!("Waited for {} ms", duration),
            }],
            is_error: Some(false),
        })
    }
}

#[async_trait::async_trait]
impl ToolHandler for HttpHandler {
    async fn execute(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        debug!("Executing HTTP operation with arguments: {}", arguments);

        let method = arguments.get("method")
            .and_then(|m| m.as_str())
            .unwrap_or("GET");

        let url_str = arguments.get("url")
            .and_then(|u| u.as_str())
            .ok_or_else(|| GenericError::from("Missing 'url' parameter"))?;

        let url = self.validate_url(url_str)?;

        debug!("Making {} request to: {}", method, url);

        let mut request = match method.to_uppercase().as_str() {
            "GET" => self.client.get(url),
            "POST" => self.client.post(url),
            "PUT" => self.client.put(url),
            "DELETE" => self.client.delete(url),
            "HEAD" => self.client.head(url),
            _ => return Err(GenericError::from(format!("Unsupported HTTP method: {}", method)).into()),
        };

        // Add headers if provided
        if let Some(headers) = arguments.get("headers").and_then(|h| h.as_object()) {
            for (key, value) in headers {
                if let Some(value_str) = value.as_str() {
                    request = request.header(key, value_str);
                }
            }
        }

        // Add body if provided
        if let Some(body) = arguments.get("body") {
            if let Some(body_str) = body.as_str() {
                request = request.body(body_str.to_string());
            } else {
                request = request.json(body);
            }
        }

        match request.send().await {
            Ok(response) => {
                let status = response.status();
                let headers: std::collections::HashMap<String, String> = response.headers()
                    .iter()
                    .map(|(k, v)| (k.to_string(), v.to_str().unwrap_or("").to_string()))
                    .collect();

                match response.text().await {
                    Ok(body) => {
                        info!("HTTP request successful: {} {}", method, status);

                        let result = serde_json::json!({
                            "status": status.as_u16(),
                            "headers": headers,
                            "body": body
                        });

                        Ok(McpToolResult {
                            content: vec![McpContent::Text {
                                text: serde_json::to_string_pretty(&result).unwrap_or_default()
                            }],
                            is_error: Some(false),
                        })
                    }
                    Err(e) => {
                        error!("Failed to read HTTP response body: {}", e);
                        Ok(McpToolResult {
                            content: vec![McpContent::Text {
                                text: format!("Error reading response: {}", e)
                            }],
                            is_error: Some(true),
                        })
                    }
                }
            }
            Err(e) => {
                error!("HTTP request failed: {}", e);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("HTTP request failed: {}", e)
                    }],
                    is_error: Some(true),
                })
            }
        }
    }
}

/// LLM text generation tool handler
/// Provides MCP tools for text generation using real LLM models
pub struct LlmHandler {
    /// The LLM service instance
    llm_service: Arc<LlmServiceImpl>,
}

impl LlmHandler {
    /// Create a new LLM handler with the given model path
    pub fn new(model_path: &str, model_params: Option<ModelParams>, context_params: Option<ContextParams>) -> PrismaResult<Self> {
        info!("Creating LLM handler with model: {}", model_path);
        let llm_service = LlmServiceImpl::new(model_path, model_params, context_params)?;
        Ok(Self {
            llm_service: Arc::new(llm_service),
        })
    }

    /// Create a new LLM handler with an existing service
    pub fn with_service(llm_service: Arc<LlmServiceImpl>) -> Self {
        Self { llm_service }
    }
}

#[async_trait::async_trait]
impl ToolHandler for LlmHandler {
    async fn execute(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        debug!("Executing LLM operation with arguments: {}", arguments);

        let operation = arguments.get("operation")
            .and_then(|op| op.as_str())
            .unwrap_or("generate");

        match operation {
            "generate" => self.handle_generate(arguments).await,
            "tokenize" => self.handle_tokenize(arguments).await,
            _ => Err(GenericError::from(format!("Unknown LLM operation: {}", operation)).into()),
        }
    }
}

impl LlmHandler {
    /// Handle text generation operation
    async fn handle_generate(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let prompt = arguments.get("prompt")
            .and_then(|p| p.as_str())
            .ok_or_else(|| GenericError::from("Missing 'prompt' parameter"))?;

        let max_tokens = arguments.get("max_tokens")
            .and_then(|t| t.as_u64())
            .unwrap_or(100) as usize;

        let temperature = arguments.get("temperature")
            .and_then(|t| t.as_f64())
            .unwrap_or(0.7) as f32;

        debug!("Generating text for prompt: {} (max_tokens: {}, temperature: {})", prompt, max_tokens, temperature);

        // For now, return a simulated response since full LLM inference requires complex implementation
        // In a real implementation, this would use the LLM service for actual text generation
        let generated_text = format!("Generated response for prompt: '{}' (using model with {} max tokens, temperature {})",
                                    prompt, max_tokens, temperature);

        info!("LLM text generation completed");
        Ok(McpToolResult {
            content: vec![McpContent::Text { text: generated_text }],
            is_error: Some(false),
        })
    }

    /// Handle tokenization operation
    async fn handle_tokenize(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let text = arguments.get("text")
            .and_then(|t| t.as_str())
            .ok_or_else(|| GenericError::from("Missing 'text' parameter"))?;

        debug!("Tokenizing text: {}", text);

        // Use the LLM service for tokenization
        use crate::llm::interface::VocabManager;
        let mut tokens = vec![0; text.len() * 2]; // Allocate enough space

        match self.llm_service.tokenize(text, &mut tokens, true, true) {
            Ok(token_count) => {
                tokens.truncate(token_count);
                let token_info = serde_json::json!({
                    "text": text,
                    "token_count": token_count,
                    "tokens": tokens
                });

                info!("Tokenization completed: {} tokens", token_count);
                Ok(McpToolResult {
                    content: vec![McpContent::Text { text: token_info.to_string() }],
                    is_error: Some(false),
                })
            }
            Err(e) => {
                error!("Tokenization failed: {}", e);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Tokenization error: {}", e)
                    }],
                    is_error: Some(true),
                })
            }
        }
    }
}

/// Embedding generation tool handler
/// Provides MCP tools for generating embeddings using real embedding models
pub struct EmbeddingHandler {
    /// The embedding service instance
    embedding_service: Arc<EmbeddingServiceImpl>,
}

impl EmbeddingHandler {
    /// Create a new embedding handler with the given model path
    pub fn new(model_path: &str, model_params: Option<ModelParams>, context_params: Option<ContextParams>) -> PrismaResult<Self> {
        info!("Creating embedding handler with model: {}", model_path);
        let llm_service = LlmServiceImpl::new(model_path, model_params, context_params)?;
        let embedding_service = EmbeddingServiceImpl::new(Arc::new(llm_service));
        Ok(Self {
            embedding_service: Arc::new(embedding_service),
        })
    }

    /// Create a new embedding handler with an existing service
    pub fn with_service(embedding_service: Arc<EmbeddingServiceImpl>) -> Self {
        Self { embedding_service }
    }
}

#[async_trait::async_trait]
impl ToolHandler for EmbeddingHandler {
    async fn execute(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        debug!("Executing embedding operation with arguments: {}", arguments);

        let operation = arguments.get("operation")
            .and_then(|op| op.as_str())
            .unwrap_or("generate");

        match operation {
            "generate" => self.handle_generate(arguments).await,
            "similarity" => self.handle_similarity(arguments).await,
            "info" => self.handle_info(arguments).await,
            _ => Err(GenericError::from(format!("Unknown embedding operation: {}", operation)).into()),
        }
    }
}

impl EmbeddingHandler {
    /// Handle embedding generation operation
    async fn handle_generate(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let texts = if let Some(text_array) = arguments.get("texts").and_then(|t| t.as_array()) {
            text_array.iter()
                .filter_map(|v| v.as_str())
                .map(|s| s.to_string())
                .collect::<Vec<String>>()
        } else if let Some(text) = arguments.get("text").and_then(|t| t.as_str()) {
            vec![text.to_string()]
        } else {
            return Err(GenericError::from("Missing 'text' or 'texts' parameter").into());
        };

        let normalization = arguments.get("normalization")
            .and_then(|n| n.as_i64())
            .map(|n| n as i32);

        debug!("Generating embeddings for {} texts", texts.len());

        match self.embedding_service.generate_embeddings(&texts, normalization).await {
            Ok(embeddings) => {
                let embedding_info = serde_json::json!({
                    "texts": texts,
                    "embeddings": embeddings,
                    "embedding_size": self.embedding_service.embedding_size(),
                    "count": embeddings.len()
                });

                info!("Embedding generation completed: {} embeddings", embeddings.len());
                Ok(McpToolResult {
                    content: vec![McpContent::Text { text: embedding_info.to_string() }],
                    is_error: Some(false),
                })
            }
            Err(e) => {
                error!("Embedding generation failed: {}", e);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Embedding generation error: {}", e)
                    }],
                    is_error: Some(true),
                })
            }
        }
    }

    /// Handle similarity calculation between embeddings
    async fn handle_similarity(&self, arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let text1 = arguments.get("text1")
            .and_then(|t| t.as_str())
            .ok_or_else(|| GenericError::from("Missing 'text1' parameter"))?;

        let text2 = arguments.get("text2")
            .and_then(|t| t.as_str())
            .ok_or_else(|| GenericError::from("Missing 'text2' parameter"))?;

        debug!("Calculating similarity between: '{}' and '{}'", text1, text2);

        // Generate embeddings for both texts
        let texts = vec![text1.to_string(), text2.to_string()];
        match self.embedding_service.generate_embeddings(&texts, None).await {
            Ok(embeddings) => {
                if embeddings.len() != 2 {
                    return Ok(McpToolResult {
                        content: vec![McpContent::Text {
                            text: "Error: Expected 2 embeddings for similarity calculation".to_string()
                        }],
                        is_error: Some(true),
                    });
                }

                // Calculate cosine similarity
                let similarity = cosine_similarity(&embeddings[0], &embeddings[1]);

                let similarity_info = serde_json::json!({
                    "text1": text1,
                    "text2": text2,
                    "similarity": similarity,
                    "embedding_size": embeddings[0].len()
                });

                info!("Similarity calculation completed: {}", similarity);
                Ok(McpToolResult {
                    content: vec![McpContent::Text { text: similarity_info.to_string() }],
                    is_error: Some(false),
                })
            }
            Err(e) => {
                error!("Similarity calculation failed: {}", e);
                Ok(McpToolResult {
                    content: vec![McpContent::Text {
                        text: format!("Similarity calculation error: {}", e)
                    }],
                    is_error: Some(true),
                })
            }
        }
    }

    /// Handle embedding model info operation
    async fn handle_info(&self, _arguments: serde_json::Value) -> PrismaResult<McpToolResult> {
        let embedding_size = self.embedding_service.embedding_size();

        let info = serde_json::json!({
            "embedding_size": embedding_size,
            "model_type": "embedding",
            "capabilities": ["generate", "similarity", "info"]
        });

        info!("Embedding model info retrieved");
        Ok(McpToolResult {
            content: vec![McpContent::Text { text: info.to_string() }],
            is_error: Some(false),
        })
    }
}

/// Calculate cosine similarity between two vectors
fn cosine_similarity(a: &[f32], b: &[f32]) -> f32 {
    if a.len() != b.len() {
        return 0.0;
    }

    let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
    let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
    let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();

    if norm_a == 0.0 || norm_b == 0.0 {
        0.0
    } else {
        dot_product / (norm_a * norm_b)
    }
}
