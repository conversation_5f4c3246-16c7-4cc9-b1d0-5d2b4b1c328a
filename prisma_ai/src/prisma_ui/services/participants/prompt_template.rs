use serde::Deserialize;
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::sync::{OnceLock, Mutex, Arc}; // Using OnceLock for caching and Mutex for reloading
use tera::{Context, Tera};
use tracing::{error, warn, info, debug};
use anyhow::{Result, Context as AnyhowContext, anyhow}; // Using anyhow for simpler error handling initially

// MCP integration imports
use crate::mcp::{McpToolsRegistry, McpIntegrationManager, ToolSource};
use crate::mcp::tools::registry::{RegisteredTool, ToolExecutionResult};

// --- Configuration Structs ---

#[derive(Deserialize, Debug, Clone, Default)]
pub struct AgentConfig {
    #[serde(default)]
    pub name: String,
    #[serde(default)]
    pub role: String,
    #[serde(default)]
    pub goal: String,
    #[serde(default = "default_template_key")]
    pub template_key: String,
    #[serde(default)]
    pub background: String,
    #[serde(default)]
    pub tasks: String,
    #[serde(default)]
    pub rules_and_skills: String,
    #[serde(default)]
    pub available_tools: Vec<String>,
    #[serde(default)]
    pub rag_config: String,
    #[serde(default)]
    pub streaming_enabled: bool,
    #[serde(default)]
    pub streaming_buffer_size: Option<usize>,
    #[serde(default)]
    pub mcp_enabled: bool,
    #[serde(default)]
    pub browser_automation_enabled: bool,
    #[serde(default)]
    pub filesystem_enabled: bool,
}

// Default template key function for serde
fn default_template_key() -> String {
    "default".to_string()
}

#[derive(Deserialize, Debug, Clone, Default)]
pub struct PmConfig {
    #[serde(default)]
    pub agents: HashMap<String, AgentConfig>,
    #[serde(default)]
    pub user_name: String,
    #[serde(default)]
    pub project_name: String,
    #[serde(default)]
    pub project_description: String,
    #[serde(default)]
    pub project_specifications: String,
    #[serde(default)]
    pub document_settings: String,
    #[serde(default)]
    pub chat: Option<ChatConfig>,
}

#[derive(Deserialize, Debug, Clone, Default)]
pub struct ChatConfig {
    #[serde(default)]
    pub streaming: Option<StreamingConfig>,
    #[serde(default)]
    pub ai_behavior: Option<AiBehaviorConfig>,
}

#[derive(Deserialize, Debug, Clone, Default)]
pub struct StreamingConfig {
    #[serde(default)]
    pub enabled: bool,
    #[serde(default)]
    pub buffer_size: usize,
    #[serde(default)]
    pub token_delay_ms: u64,
    #[serde(default)]
    pub show_typing_indicator: bool,
    #[serde(default)]
    pub show_partial_messages: bool,
    #[serde(default)]
    pub use_markdown_rendering: bool,
}

#[derive(Deserialize, Debug, Clone, Default)]
pub struct AiBehaviorConfig {
    #[serde(default)]
    pub simulate_typing: bool,
    #[serde(default)]
    pub auto_scroll_on_response: bool,
    #[serde(default)]
    pub streaming_enabled: bool,
    #[serde(default)]
    pub streaming_buffer_size: usize,
}

#[derive(Deserialize, Debug, Clone, Default)]
pub struct PromptTemplates {
    #[serde(default)]
    pub templates: HashMap<String, String>,
}

// --- Cached Configuration ---

static CONFIG_CACHE: OnceLock<Mutex<Option<ConfigCache>>> = OnceLock::new();

#[derive(Debug, Clone)]
pub struct ConfigCache {
    pub pm_config: PmConfig,
    pub tera: Tera, // Store compiled templates
}

// --- Configuration Paths ---

// Default paths relative to crate root
const DEFAULT_PM_PATH: &str = "src/config/pm.toml";
const DEFAULT_TEMPLATES_PATH: &str = "src/config/prompt_template.toml";

// --- Loading and Caching ---

/// Initialize the configuration cache if not already initialized
pub fn init_config_cache() -> Result<()> {
    if CONFIG_CACHE.get().is_none() {
        let _ = CONFIG_CACHE.get_or_init(|| Mutex::new(None));
        reload_configs(None, None)?;
    }
    Ok(())
}

/// Reload configurations from the specified paths or default paths
pub fn reload_configs(pm_path: Option<&Path>, templates_path: Option<&Path>) -> Result<()> {
    let pm_path = pm_path.unwrap_or_else(|| Path::new(DEFAULT_PM_PATH));
    let templates_path = templates_path.unwrap_or_else(|| Path::new(DEFAULT_TEMPLATES_PATH));

    info!("Loading configurations from {:?} and {:?}", pm_path, templates_path);

    // Load pm.toml
    let pm_config = load_pm_config(pm_path)?;

    // Load prompt_template.toml
    let prompt_templates = load_prompt_templates(templates_path)?;

    // Initialize Tera and add templates
    let mut tera = Tera::default();

    // Add templates directly from the loaded map
    if prompt_templates.templates.is_empty() {
        warn!("No templates found in {:?}", templates_path);
    } else {
        // First, add all templates to Tera
        for (key, template_str) in prompt_templates.templates.iter() {
            tera.add_raw_template(key, template_str)
                .with_context(|| format!("Failed to add template '{}' to Tera", key))?;
        }

        // Enable auto-escape to prevent issues with JSON in templates
        tera.autoescape_on(vec![]);

        // Enable template inheritance
        tera.build_inheritance_chains()
            .with_context(|| "Failed to build template inheritance chains")?;

        debug!("Added {} templates to Tera", prompt_templates.templates.len());
    }

    // Update the cache
    let cache = ConfigCache { pm_config, tera };

    let mutex = CONFIG_CACHE.get().expect("CONFIG_CACHE should be initialized");
    let mut guard = mutex.lock().map_err(|_| anyhow!("Failed to acquire lock on CONFIG_CACHE"))?;
    *guard = Some(cache);

    info!("Configurations loaded and templates compiled successfully.");
    Ok(())
}

/// Load the pm.toml configuration file
fn load_pm_config(path: &Path) -> Result<PmConfig> {
    match fs::read_to_string(path) {
        Ok(content) => {
            if content.trim().is_empty() {
                warn!("pm.toml is empty, using default configuration");
                return Ok(PmConfig::default());
            }

            match toml::from_str(&content) {
                Ok(config) => Ok(config),
                Err(e) => {
                    error!("Failed to parse pm.toml: {}", e);
                    Err(anyhow!("Failed to parse pm.toml: {}", e))
                }
            }
        },
        Err(e) => {
            warn!("Failed to read pm.toml, using default configuration: {}", e);
            Ok(PmConfig::default())
        }
    }
}

/// Load the prompt_template.toml configuration file
fn load_prompt_templates(path: &Path) -> Result<PromptTemplates> {
    match fs::read_to_string(path) {
        Ok(content) => {
            if content.trim().is_empty() {
                warn!("prompt_template.toml is empty, using default templates");
                return Ok(PromptTemplates::default());
            }

            match toml::from_str(&content) {
                Ok(templates) => Ok(templates),
                Err(e) => {
                    error!("Failed to parse prompt_template.toml: {}", e);
                    Err(anyhow!("Failed to parse prompt_template.toml: {}", e))
                }
            }
        },
        Err(e) => {
            warn!("Failed to read prompt_template.toml, using default templates: {}", e);
            Ok(PromptTemplates::default())
        }
    }
}

/// Get the cached configuration
pub fn get_config() -> Result<ConfigCache> {
    // Initialize if not already initialized
    init_config_cache()?;

    let mutex = CONFIG_CACHE.get().expect("CONFIG_CACHE should be initialized");
    let guard = mutex.lock().map_err(|_| anyhow!("Failed to acquire lock on CONFIG_CACHE"))?;

    match &*guard {
        Some(cache) => Ok(cache.clone()),
        None => Err(anyhow!("Configuration cache is not initialized"))
    }
}

// --- Prompt Construction ---

/// Constructs the final prompt for an agent using cached configurations and templates.
///
/// # Arguments
/// * `agent_id` - The ID of the agent for which to construct the prompt.
/// * `agent_config` - The configuration for the agent, containing role, goal, etc.
/// * `history` - A string representing the formatted conversation history.
/// * `user_message` - The latest message from the user.
/// * `template_key` - Optional template key to use. If None, the agent's template_key is used.
/// * `additional_context` - Optional additional context variables to include in the template.
///
/// # Returns
/// A `Result` containing the constructed prompt string or an error.
pub fn construct_prompt_with_config(
    agent_id: &str,
    agent_config: &AgentConfig,
    history: &str,
    user_message: &str,
    template_key: Option<&str>,
    additional_context: Option<HashMap<String, String>>,
) -> Result<String> {
    // Get the cached configuration
    let cache = get_config()
        .map_err(|e| {
            error!("Failed to get configuration: {:?}", e);
            anyhow!("Configuration access failed") // Return a generic error
        })?;

    // Use the provided template key or the agent's template_key
    let template_key = template_key.unwrap_or(&agent_config.template_key);

    // Create Tera context with all agent configuration fields
    let mut context = Context::new();
    context.insert("agent_id", agent_id);
    context.insert("name", &agent_config.name);
    context.insert("role", &agent_config.role);
    context.insert("goal", &agent_config.goal);
    context.insert("background", &agent_config.background);
    context.insert("tasks", &agent_config.tasks);
    context.insert("rules_and_skills", &agent_config.rules_and_skills);

    // Add RAG configuration if available
    if !agent_config.rag_config.is_empty() {
        context.insert("rag_config", &agent_config.rag_config);
    }

    // Convert available_tools to a formatted string if needed
    if !agent_config.available_tools.is_empty() {
        let tools_str = agent_config.available_tools
            .iter()
            .map(|tool| format!("- {}", tool))
            .collect::<Vec<_>>()
            .join("\n");
        context.insert("available_tools", &tools_str);
    } else {
        context.insert("available_tools", "None");
    }

    // Add project-level settings from PmConfig
    let pm_config = &cache.pm_config;
    if !pm_config.user_name.is_empty() {
        context.insert("user_name", &pm_config.user_name);
    }
    if !pm_config.project_name.is_empty() {
        context.insert("project_name", &pm_config.project_name);
    }
    if !pm_config.project_description.is_empty() {
        context.insert("project_description", &pm_config.project_description);
    }
    if !pm_config.project_specifications.is_empty() {
        context.insert("project_specifications", &pm_config.project_specifications);
    }
    if !pm_config.document_settings.is_empty() {
        context.insert("document_settings", &pm_config.document_settings);
    }

    // Add streaming settings if available
    if let Some(chat_config) = &pm_config.chat {
        if let Some(streaming_config) = &chat_config.streaming {
            context.insert("streaming_enabled", &streaming_config.enabled);
            context.insert("streaming_buffer_size", &streaming_config.buffer_size);
        }

        if let Some(ai_behavior) = &chat_config.ai_behavior {
            context.insert("ai_streaming_enabled", &ai_behavior.streaming_enabled);
            context.insert("ai_streaming_buffer_size", &ai_behavior.streaming_buffer_size);
        }
    }

    // Add agent-specific streaming settings
    context.insert("agent_streaming_enabled", &agent_config.streaming_enabled);
    if let Some(buffer_size) = agent_config.streaming_buffer_size {
        context.insert("agent_streaming_buffer_size", &buffer_size);
    }

    // Add conversation context
    context.insert("history", history);
    context.insert("user_msg", user_message);

    // Add any additional context variables
    if let Some(additional_vars) = additional_context {
        for (key, value) in additional_vars {
            context.insert(key, &value);
        }
    }

    // Render the specified template
    cache.tera.render(template_key, &context)
        .with_context(|| format!("Failed to render template '{}' for agent '{}'", template_key, agent_id))
}

/// Constructs the final prompt for an agent with MCP integration support.
///
/// # Arguments
/// * `agent_id` - The ID of the agent for which to construct the prompt.
/// * `agent_config` - The agent configuration.
/// * `history` - A string representing the formatted conversation history.
/// * `user_message` - The latest message from the user.
/// * `template_key` - Optional template key to use. If None, the agent's template_key is used.
/// * `additional_context` - Optional additional context variables to include in the template.
/// * `tools_registry` - Optional MCP tools registry for dynamic tool discovery.
/// * `tool_results` - Optional recent tool execution results.
///
/// # Returns
/// A `Result` containing the constructed prompt string or an error.
pub async fn construct_prompt_with_mcp(
    agent_id: &str,
    agent_config: &AgentConfig,
    history: &str,
    user_message: &str,
    template_key: Option<&str>,
    additional_context: Option<HashMap<String, String>>,
    tools_registry: Option<Arc<McpToolsRegistry>>,
    tool_results: Option<&[ToolExecutionResult]>,
) -> Result<String> {
    // Get the cached configuration
    let cache = get_config()
        .map_err(|e| {
            error!("Failed to get configuration: {:?}", e);
            anyhow!("Configuration access failed")
        })?;

    // Use the provided template key or the agent's template_key
    let template_key = template_key.unwrap_or(&agent_config.template_key);

    // Create Tera context with all agent configuration fields
    let mut context = Context::new();
    context.insert("agent_id", agent_id);
    context.insert("name", &agent_config.name);
    context.insert("role", &agent_config.role);
    context.insert("goal", &agent_config.goal);
    context.insert("background", &agent_config.background);
    context.insert("tasks", &agent_config.tasks);
    context.insert("rules_and_skills", &agent_config.rules_and_skills);

    // Add RAG configuration if available
    if !agent_config.rag_config.is_empty() {
        context.insert("rag_config", &agent_config.rag_config);
    }

    // Convert available_tools to a formatted string if needed
    if !agent_config.available_tools.is_empty() {
        let tools_str = agent_config.available_tools
            .iter()
            .map(|tool| format!("- {}", tool))
            .collect::<Vec<_>>()
            .join("\n");
        context.insert("available_tools", &tools_str);
    } else {
        context.insert("available_tools", "None");
    }

    // Add MCP tools if enabled and registry is available
    if agent_config.mcp_enabled {
        match get_mcp_tools_for_template(tools_registry.clone()).await {
            Ok(mcp_tools) => {
                if !mcp_tools.is_empty() {
                    context.insert("mcp_tools", &mcp_tools);
                    debug!("Added {} MCP tools to template context", mcp_tools.len());
                }
            }
            Err(e) => {
                warn!("Failed to get MCP tools for template: {}", e);
            }
        }
    }

    // Add browser automation tools if enabled
    if agent_config.browser_automation_enabled {
        match get_browser_tools_for_template(tools_registry.clone()).await {
            Ok(browser_tools) => {
                if !browser_tools.is_empty() {
                    context.insert("browser_tools", &browser_tools);
                    debug!("Added {} browser tools to template context", browser_tools.len());
                }
            }
            Err(e) => {
                warn!("Failed to get browser tools for template: {}", e);
            }
        }
    }

    // Add filesystem tools if enabled
    if agent_config.filesystem_enabled {
        match get_filesystem_tools_for_template(tools_registry.clone()).await {
            Ok(filesystem_tools) => {
                if !filesystem_tools.is_empty() {
                    context.insert("filesystem_tools", &filesystem_tools);
                    debug!("Added {} filesystem tools to template context", filesystem_tools.len());
                }
            }
            Err(e) => {
                warn!("Failed to get filesystem tools for template: {}", e);
            }
        }
    }

    // Add MCP tool execution results if available
    if let Some(results) = tool_results {
        if !results.is_empty() {
            let formatted_results = format_mcp_tool_results(results);
            context.insert("mcp_tool_results", &formatted_results);
            debug!("Added {} tool execution results to template context", formatted_results.len());
        }
    }

    // Add project-level settings from PmConfig
    let pm_config = &cache.pm_config;
    if !pm_config.user_name.is_empty() {
        context.insert("user_name", &pm_config.user_name);
    }
    if !pm_config.project_name.is_empty() {
        context.insert("project_name", &pm_config.project_name);
    }
    if !pm_config.project_description.is_empty() {
        context.insert("project_description", &pm_config.project_description);
    }
    if !pm_config.project_specifications.is_empty() {
        context.insert("project_specifications", &pm_config.project_specifications);
    }
    if !pm_config.document_settings.is_empty() {
        context.insert("document_settings", &pm_config.document_settings);
    }

    // Add streaming settings if available
    if let Some(chat_config) = &pm_config.chat {
        if let Some(streaming_config) = &chat_config.streaming {
            context.insert("streaming_enabled", &streaming_config.enabled);
            context.insert("streaming_buffer_size", &streaming_config.buffer_size);
        }

        if let Some(ai_behavior) = &chat_config.ai_behavior {
            context.insert("ai_streaming_enabled", &ai_behavior.streaming_enabled);
            context.insert("ai_streaming_buffer_size", &ai_behavior.streaming_buffer_size);
        }
    }

    // Add agent-specific streaming settings
    context.insert("agent_streaming_enabled", &agent_config.streaming_enabled);
    if let Some(buffer_size) = agent_config.streaming_buffer_size {
        context.insert("agent_streaming_buffer_size", &buffer_size);
    }

    // Add conversation context
    context.insert("history", history);
    context.insert("user_msg", user_message);

    // Add any additional context variables
    if let Some(additional_vars) = additional_context {
        for (key, value) in additional_vars {
            context.insert(key, &value);
        }
    }

    // Render the specified template
    cache.tera.render(template_key, &context)
        .with_context(|| format!("Failed to render template '{}' for agent '{}'", template_key, agent_id))
}

/// Constructs the final prompt for an agent using cached configurations and templates.
///
/// # Arguments
/// * `agent_id` - The ID of the agent for which to construct the prompt.
/// * `role` - The role of the agent.
/// * `goal` - The goal of the agent.
/// * `history` - A string representing the formatted conversation history.
/// * `user_message` - The latest message from the user.
/// * `template_key` - Optional template key to use. If None, "default" is used.
///
/// # Returns
/// A `Result` containing the constructed prompt string or an error.
pub fn construct_prompt(
    agent_id: &str,
    role: &str,
    goal: &str,
    history: &str,
    user_message: &str,
    template_key: Option<&str>,
) -> Result<String> {
    // Get the cached configuration
    let cache = get_config()
        .map_err(|e| {
            error!("Failed to get configuration: {:?}", e);
            anyhow!("Configuration access failed") // Return a generic error
        })?;

    // Use the provided template key or default to "default"
    let template_key = template_key.unwrap_or("default");

    // Create a minimal agent config with just the required fields
    let agent_config = AgentConfig {
        name: agent_id.to_string(),
        role: role.to_string(),
        goal: goal.to_string(),
        template_key: template_key.to_string(),
        ..AgentConfig::default()
    };

    // Use the more comprehensive function
    construct_prompt_with_config(
        agent_id,
        &agent_config,
        history,
        user_message,
        Some(template_key),
        None,
    )
}

/// Constructs the final prompt for an agent using cached configurations and templates.
/// This version looks up the agent configuration from pm.toml
///
/// # Arguments
/// * `agent_id` - The ID of the agent for which to construct the prompt.
/// * `history` - A string representing the formatted conversation history.
/// * `user_message` - The latest message from the user.
///
/// # Returns
/// A `Result` containing the constructed prompt string or an error.
pub fn construct_prompt_from_config(
    agent_id: &str,
    history: &str,
    user_message: &str,
) -> Result<String> {
    // Get the cached configuration
    let cache = get_config()
        .map_err(|e| {
            error!("Failed to get configuration: {:?}", e);
            anyhow!("Configuration access failed") // Return a generic error
        })?;

    // Find agent config using the Data Retrieval step
    // This retrieves role, goal, template_key, and template string
    debug!("Retrieving agent data for agent_id: {}", agent_id);
    let agent_config = cache.pm_config.agents.get(agent_id)
        .ok_or_else(|| anyhow!("Agent config not found for agent_id: {}", agent_id))?;

    debug!("Retrieved agent data: role={}, goal={}, template_key={}",
           agent_config.role, agent_config.goal, agent_config.template_key);

    // Use the more comprehensive function with the full agent config
    construct_prompt_with_config(
        agent_id,
        agent_config,
        history,
        user_message,
        None, // Use the agent's template_key from its config
        None, // No additional context variables
    )
}

/// Get a specific template by key
pub fn get_template(template_key: &str) -> Result<String> {
    let cache = get_config()?;

    // Create an empty context
    let context = Context::new();

    // Render the template with an empty context
    cache.tera.render(template_key, &context)
        .with_context(|| format!("Failed to get template '{}'", template_key))
}

/// Get agent configuration by ID
pub fn get_agent_config(agent_id: &str) -> Result<Option<AgentConfig>> {
    let cache = get_config()?;
    Ok(cache.pm_config.agents.get(agent_id).cloned())
}

/// Extract agent data (role, goal, template_key) and the corresponding template string
/// This function implements the "Data Retrieval" step of Dynamic Prompt Construction
///
/// # Arguments
/// * `agent_id` - The ID of the agent for which to retrieve data
///
/// # Returns
/// A `Result` containing a tuple with the agent's role, goal, template_key, and the template string
pub fn retrieve_agent_data(agent_id: &str) -> Result<(String, String, String, String)> {
    // Get the agent configuration from pm.toml
    let agent_config = get_agent_config(agent_id)?
        .ok_or_else(|| anyhow!("Agent config not found for agent_id: {}", agent_id))?;

    // Extract the specific fields we need
    let role = agent_config.role.clone();
    let goal = agent_config.goal.clone();
    let template_key = agent_config.template_key.clone();

    // Get the template string using the template_key
    let template_string = get_template(&template_key)?;

    // Return the extracted data and template string
    Ok((role, goal, template_key, template_string))
}

/// List all available template keys
pub fn list_templates() -> Result<Vec<String>> {
    let cache = get_config()?;
    Ok(cache.tera.get_template_names().map(|s| s.to_string()).collect())
}

/// List all available agent IDs
pub fn list_agent_ids() -> Result<Vec<String>> {
    let cache = get_config()?;
    Ok(cache.pm_config.agents.keys().cloned().collect())
}

/// Demonstrates the complete flow of Dynamic Prompt Construction:
/// 1. Data Retrieval: Extract agent data from pm.toml and get template from prompt_template.toml
/// 2. Templating: Inject the data into the template
///
/// # Arguments
/// * `agent_id` - The ID of the agent for which to construct the prompt.
/// * `history` - A string representing the formatted conversation history.
/// * `user_message` - The latest message from the user.
///
/// # Returns
/// A `Result` containing the constructed prompt string or an error.
pub fn dynamic_prompt_construction(
    agent_id: &str,
    history: &str,
    user_message: &str,
) -> Result<String> {
    // Step 1: Data Retrieval
    info!("Step 1: Data Retrieval for agent_id: {}", agent_id);
    let (role, goal, template_key, _template_string) = retrieve_agent_data(agent_id)?;
    info!("Retrieved data: role={}, goal={}, template_key={}", role, goal, template_key);

    // Step 2: Templating (using the existing construct_prompt function)
    info!("Step 2: Templating using template_key: {}", template_key);
    let prompt = construct_prompt(
        agent_id,
        &role,
        &goal,
        history,
        user_message,
        Some(&template_key),
    )?;

    info!("Dynamic prompt construction completed successfully");
    Ok(prompt)
}

// ===== MCP Integration Functions =====

/// MCP tool information for template rendering
#[derive(Debug, Clone, serde::Serialize)]
pub struct McpToolInfo {
    pub name: String,
    pub description: String,
    pub source: String,
    pub input_schema: serde_json::Value,
    pub enabled: bool,
}

/// MCP tool execution result for template rendering
#[derive(Debug, Clone, serde::Serialize)]
pub struct McpToolResultInfo {
    pub tool_name: String,
    pub source: String,
    pub status: String,
    pub success: bool,
    pub content: String,
    pub error: Option<String>,
    pub duration_ms: u64,
}

/// Fetch MCP tools from the registry and format them for template rendering
pub async fn get_mcp_tools_for_template(
    tools_registry: Option<Arc<McpToolsRegistry>>,
) -> Result<Vec<McpToolInfo>> {
    let Some(registry) = tools_registry else {
        debug!("No MCP tools registry provided");
        return Ok(Vec::new());
    };

    let tools = registry.get_tools().await;
    let mut mcp_tools = Vec::new();

    for (_, tool) in tools {
        if tool.enabled {
            let source_str = match &tool.source {
                ToolSource::Local => "Local".to_string(),
                ToolSource::Remote { server_name } => format!("Remote ({})", server_name),
                ToolSource::Marketplace { package_name, version } => {
                    format!("Marketplace ({}:{})", package_name, version)
                }
            };

            mcp_tools.push(McpToolInfo {
                name: tool.name,
                description: tool.description,
                source: source_str,
                input_schema: tool.input_schema,
                enabled: tool.enabled,
            });
        }
    }

    debug!("Retrieved {} MCP tools for template", mcp_tools.len());
    Ok(mcp_tools)
}

/// Get browser automation tools (Playwright MCP tools)
pub async fn get_browser_tools_for_template(
    tools_registry: Option<Arc<McpToolsRegistry>>,
) -> Result<Vec<McpToolInfo>> {
    let Some(registry) = tools_registry else {
        return Ok(Vec::new());
    };

    let tools = registry.get_tools().await;
    let mut browser_tools = Vec::new();

    for (_, tool) in tools {
        if tool.enabled {
            if let ToolSource::Remote { server_name } = &tool.source {
                if server_name.contains("playwright") || server_name.contains("browser") {
                    browser_tools.push(McpToolInfo {
                        name: tool.name,
                        description: tool.description,
                        source: format!("Playwright MCP ({})", server_name),
                        input_schema: tool.input_schema,
                        enabled: tool.enabled,
                    });
                }
            }
        }
    }

    debug!("Retrieved {} browser automation tools for template", browser_tools.len());
    Ok(browser_tools)
}

/// Get filesystem tools (filesystem MCP tools)
pub async fn get_filesystem_tools_for_template(
    tools_registry: Option<Arc<McpToolsRegistry>>,
) -> Result<Vec<McpToolInfo>> {
    let Some(registry) = tools_registry else {
        return Ok(Vec::new());
    };

    let tools = registry.get_tools().await;
    let mut filesystem_tools = Vec::new();

    for (_, tool) in tools {
        if tool.enabled {
            if let ToolSource::Remote { server_name } = &tool.source {
                if server_name.contains("filesystem") || server_name.contains("file") {
                    // Add allowed directories info if available
                    let mut description = tool.description.clone();
                    if let Some(config) = &tool.config {
                        if let Some(allowed_dirs) = config.get("allowed_directories") {
                            description = format!("{} (Allowed: {})", description, allowed_dirs);
                        }
                    }

                    filesystem_tools.push(McpToolInfo {
                        name: tool.name,
                        description,
                        source: format!("Filesystem MCP ({})", server_name),
                        input_schema: tool.input_schema,
                        enabled: tool.enabled,
                    });
                }
            }
        }
    }

    debug!("Retrieved {} filesystem tools for template", filesystem_tools.len());
    Ok(filesystem_tools)
}

/// Format MCP tool execution results for template rendering
pub fn format_mcp_tool_results(results: &[ToolExecutionResult]) -> Vec<McpToolResultInfo> {
    results
        .iter()
        .map(|result| {
            let source_str = match &result.metadata.source {
                ToolSource::Local => "Local".to_string(),
                ToolSource::Remote { server_name } => format!("Remote ({})", server_name),
                ToolSource::Marketplace { package_name, version } => {
                    format!("Marketplace ({}:{})", package_name, version)
                }
            };

            let content = if let Some(first_content) = result.result.content.first() {
                match first_content {
                    crate::mcp::McpContent::Text { text } => text.clone(),
                    crate::mcp::McpContent::Image { data, mime_type } => {
                        format!("Image ({}, {} bytes)", mime_type, data.len())
                    }
                    crate::mcp::McpContent::Resource { resource } => {
                        format!("Resource: {} ({})", resource.uri, resource.mime_type.as_deref().unwrap_or("unknown"))
                    }
                }
            } else {
                "No content".to_string()
            };

            McpToolResultInfo {
                tool_name: "Unknown".to_string(), // This would need to be tracked separately
                source: source_str,
                status: if result.metadata.success { "Success" } else { "Failed" }.to_string(),
                success: result.metadata.success,
                content,
                error: result.metadata.error.clone(),
                duration_ms: result.metadata.duration_ms,
            }
        })
        .collect()
}