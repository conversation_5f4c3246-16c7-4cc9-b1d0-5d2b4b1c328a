# Prompt Templates for Prisma AI
# These templates use the Tera templating engine syntax

[templates]

# Common sections that can be reused across templates
# These are not meant to be used directly as templates

# Common conversation footer - appears in all templates
# Variables: history, user_msg
conversation_footer = """
Conversation history:
{{ history }}

User: {{ user_msg }}
Assistant: """

# Common goal section - appears in most templates
# Variables: goal
goal_section = "Your goal is: {{ goal }}"

# Basic role introduction - appears in simple templates
# Variables: role
role_intro = "You are an AI assistant with the following role: {{ role }}"

# Named role introduction - appears in templates with agent names
# Variables: name, role
named_role_intro = "You are {{ name }}, an AI assistant with the following role: {{ role }}"

# Document settings section - appears in templates with document access
# Variables: document_settings
document_settings_section = """
Document settings for this project:
{{ document_settings }}"""

# RAG configuration section - appears in templates with RAG capabilities
# Variables: rag_config
rag_config_section = """
You have access to the following document retrieval configuration:
{{ rag_config }}

When referencing information from documents, cite the source."""

# Long-term memory section - appears in templates with LTM capabilities
# Variables: long_term_memory
long_term_memory_section = """
You have access to the following relevant memories from your long-term memory:
{{ long_term_memory }}

Use these memories to inform your responses when relevant."""

# MCP tools section - appears in MCP-enabled templates
# Variables: mcp_tools
mcp_tools_section = """
You have access to the following MCP (Model Context Protocol) tools:
{% for tool in mcp_tools %}
- **{{ tool.name }}**: {{ tool.description }}
  - Source: {{ tool.source }}
  - Schema: {{ tool.input_schema | tojson }}
{% endfor %}

When you need to use an MCP tool, use the following format:
```json
{"mcp_tool": "tool_name", "parameters": {"param1": "value1", "param2": "value2"}}
```
"""

# MCP tool results section - appears when tool execution results are available
# Variables: mcp_tool_results
mcp_tool_results_section = """
Recent MCP tool execution results:
{% for result in mcp_tool_results %}
- **{{ result.tool_name }}** ({{ result.source }}) - {{ result.status }}:
  {% if result.success %}
  {{ result.content }}
  {% else %}
  Error: {{ result.error }}
  {% endif %}
  Duration: {{ result.duration_ms }}ms
{% endfor %}
"""

# Browser automation tools section - for Playwright MCP tools
# Variables: browser_tools
browser_tools_section = """
You have access to browser automation tools via Playwright MCP:
{% for tool in browser_tools %}
- **{{ tool.name }}**: {{ tool.description }}
  - Capabilities: Web browsing, accessibility snapshots, element interaction
{% endfor %}

Use these tools for web-based tasks, accessibility analysis, and browser automation.
"""

# Filesystem tools section - for filesystem MCP tools
# Variables: filesystem_tools
filesystem_tools_section = """
You have access to filesystem tools via MCP:
{% for tool in filesystem_tools %}
- **{{ tool.name }}**: {{ tool.description }}
  - Allowed directories: {{ tool.allowed_directories | join(", ") }}
{% endfor %}

Use these tools for file operations, directory listing, and content management.
"""

# Base template with common elements - to be extended by other templates
# Variables: role, goal, history, user_msg
base_template = """
{% if name is defined %}{{ named_role_intro }}{% else %}{{ role_intro }}{% endif %}

{{ goal_section }}

{% if background is defined %}Background: {{ background }}
{% endif %}
{% if tasks is defined %}Your tasks include: {{ tasks }}
{% endif %}
{% if rules_and_skills is defined %}You must follow these rules and use these skills:
{{ rules_and_skills }}
{% endif %}
{% if available_tools is defined and available_tools != "None" %}You have access to the following tools:
{{ available_tools }}

When you need to use a tool, use the following format:
```json
{"tool": "tool_name", "parameters": {"param1": "value1", "param2": "value2"}}
```
{% endif %}
{% if mcp_tools is defined %}{{ mcp_tools_section }}{% endif %}
{% if browser_tools is defined %}{{ browser_tools_section }}{% endif %}
{% if filesystem_tools is defined %}{{ filesystem_tools_section }}{% endif %}
{% if mcp_tool_results is defined %}{{ mcp_tool_results_section }}{% endif %}
{% if rag_config is defined %}{{ rag_config_section }}{% endif %}
{% if document_settings is defined %}{{ document_settings_section }}{% endif %}
{% if long_term_memory is defined %}{{ long_term_memory_section }}{% endif %}
{% if participants is defined %}You are participating in a collaborative discussion with:
{{ participants }}
{% endif %}
{% if topic is defined %}The current topic is: {{ topic }}
{% endif %}

{{ conversation_footer }}"""

# Actual templates that use the base template

# Default template - basic assistant template
# Variables: role, goal, history, user_msg
default = "{{ base_template }}"

# Comprehensive agent template - includes more detailed agent information
# Variables: name, role, background, tasks, goal, history, user_msg
comprehensive = "{{ base_template }}"

# Project-focused template - for project-related discussions
# Variables: user_name, project_name, project_description, project_specifications, document_settings, role, goal, history, user_msg
project = """
You are an AI assistant helping {{ user_name }} with their project: {{ project_name }}

Project Description: {{ project_description }}

Project Specifications: {{ project_specifications }}

Your role is: {{ role }}

{{ goal_section }}

{% if document_settings is defined %}{{ document_settings_section }}{% endif %}

{{ conversation_footer }}"""

# Rules and skills template - includes specific rules and skills for the agent
# Variables: role, goal, rules_and_skills, history, user_msg
rules_based = "{{ base_template }}"

# Tool-enabled template - for agents with tool access
# Variables: role, goal, available_tools, history, user_msg
tool_enabled = "{{ base_template }}"

# RAG-enabled template - for agents with document retrieval capabilities
# Variables: role, goal, rag_config, history, user_msg
rag_enabled = "{{ base_template }}"

# LTM-enabled template - for agents with long-term memory capabilities
# Variables: role, goal, long_term_memory, history, user_msg
ltm_enabled = "{{ base_template }}"

# Streaming-enabled template - for agents with streaming output capabilities
# Variables: role, goal, history, user_msg
streaming_enabled = "{{ base_template }}"

# Collaborative template - for multi-agent collaboration scenarios
# Variables: name, role, goal, participants, topic, history, user_msg
collaborative = "{{ base_template }}"

# MCP-enabled template - for agents with MCP tool access
# Variables: role, goal, mcp_tools, history, user_msg
mcp_enabled = "{{ base_template }}"

# Browser automation template - for agents with Playwright MCP tools
# Variables: role, goal, browser_tools, history, user_msg
browser_automation = "{{ base_template }}"

# Filesystem template - for agents with filesystem MCP tools
# Variables: role, goal, filesystem_tools, history, user_msg
filesystem_enabled = "{{ base_template }}"

# Full MCP template - for agents with comprehensive MCP tool access
# Variables: role, goal, mcp_tools, browser_tools, filesystem_tools, mcp_tool_results, history, user_msg
full_mcp = "{{ base_template }}"
